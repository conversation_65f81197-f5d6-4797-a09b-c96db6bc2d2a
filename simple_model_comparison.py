#!/usr/bin/env python3
"""
简化版本的模型对比分析脚本
"""

import json
import os

def load_accuracy_report(file_path):
    """加载准确性报告文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def main():
    # 文件路径
    llama_report_path = "reasoning_logs/accuracy_tracking_NVDA_20250101-20240601_llama4-scout/final_accuracy_report_experiment_NVDA_20250101-20250601_llama4-scout_2025-06-30.json"
    grok_report_path = "reasoning_logs/accuracy_tracking_NVDA_20250101-20240601_grok-beta/final_accuracy_report_experiment_2025-06-28_NVDA.json"
    
    # 加载报告
    llama_report = load_accuracy_report(llama_report_path)
    grok_report = load_accuracy_report(grok_report_path)
    
    if not llama_report or not grok_report:
        print("无法加载报告文件")
        return
    
    print("=" * 80)
    print("NVDA 股票预测准确性对比分析：llama4-scout vs grok-beta")
    print("=" * 80)
    
    # 基本信息对比
    print(f"\n📊 基本信息对比:")
    print(f"llama4-scout - 总评估次数: {llama_report['total_evaluations']}, 代理数量: {llama_report['agents_tracked']}")
    print(f"grok-beta    - 总评估次数: {grok_report['total_evaluations']}, 代理数量: {grok_report['agents_tracked']}")
    
    # 计算平均准确率
    llama_avg = sum(agent['accuracy_rate'] for agent in llama_report['cumulative_stats'].values()) / len(llama_report['cumulative_stats'])
    grok_avg = sum(agent['accuracy_rate'] for agent in grok_report['cumulative_stats'].values()) / len(grok_report['cumulative_stats'])
    
    print(f"\n📈 平均准确率对比:")
    print(f"llama4-scout: {llama_avg:.4f}")
    print(f"grok-beta:    {grok_avg:.4f}")
    print(f"差异:         {llama_avg - grok_avg:+.4f}")
    
    # 各代理准确率对比
    print(f"\n🤖 各代理准确率对比:")
    print(f"{'代理名称':<35} {'llama4-scout':<12} {'grok-beta':<12} {'差异':<10} {'胜负':<8}")
    print("-" * 80)
    
    llama_wins = 0
    grok_wins = 0
    ties = 0
    
    for agent_name in sorted(llama_report['cumulative_stats'].keys()):
        if agent_name in grok_report['cumulative_stats']:
            llama_acc = llama_report['cumulative_stats'][agent_name]['accuracy_rate']
            grok_acc = grok_report['cumulative_stats'][agent_name]['accuracy_rate']
            diff = llama_acc - grok_acc
            
            if diff > 0.01:
                winner = "llama胜"
                llama_wins += 1
            elif diff < -0.01:
                winner = "grok胜"
                grok_wins += 1
            else:
                winner = "相近"
                ties += 1
            
            print(f"{agent_name:<35} {llama_acc:.4f:<12} {grok_acc:.4f:<12} {diff:+.4f:<10} {winner:<8}")
    
    print(f"\n📊 胜负统计:")
    print(f"llama4-scout 胜出: {llama_wins} 个代理")
    print(f"grok-beta 胜出:    {grok_wins} 个代理")
    print(f"表现相近:          {ties} 个代理")
    
    # 最佳表现代理
    llama_best = max(llama_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    grok_best = max(grok_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    
    print(f"\n🏆 最佳表现代理:")
    print(f"llama4-scout: {llama_best[0]} ({llama_best[1]['accuracy_rate']:.4f})")
    print(f"grok-beta:    {grok_best[0]} ({grok_best[1]['accuracy_rate']:.4f})")
    
    # 最差表现代理
    llama_worst = min(llama_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    grok_worst = min(grok_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    
    print(f"\n📉 最差表现代理:")
    print(f"llama4-scout: {llama_worst[0]} ({llama_worst[1]['accuracy_rate']:.4f})")
    print(f"grok-beta:    {grok_worst[0]} ({grok_worst[1]['accuracy_rate']:.4f})")
    
    print(f"\n✅ 分析完成！")

if __name__ == "__main__":
    main()
