#!/usr/bin/env python3
"""
分析 accuracy_tracking_NVDA_20250101-20240601_llama4-scout 文件夹中的实验数据
并生成完整的准确性统计报告
"""

import json
import os
from datetime import datetime
from collections import defaultdict
import glob

def load_daily_accuracy_files(folder_path):
    """加载所有每日准确性文件"""
    daily_files = glob.glob(os.path.join(folder_path, "daily_accuracy_*.json"))
    daily_data = []
    
    for file_path in sorted(daily_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                daily_data.append(data)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
    
    return daily_data

def calculate_agent_statistics(daily_data):
    """计算每个代理的统计数据"""
    agent_stats = defaultdict(lambda: {
        'total_predictions': 0,
        'correct_predictions': 0,
        'signal_counts': defaultdict(int),
        'signal_correct': defaultdict(int)
    })
    
    for day_data in daily_data:
        if 'agent_results' not in day_data:
            continue
            
        for agent_name, agent_result in day_data['agent_results'].items():
            stats = agent_stats[agent_name]
            signal = agent_result.get('signal', 'unknown')
            is_correct = agent_result.get('is_correct', False)
            
            stats['total_predictions'] += 1
            stats['signal_counts'][signal] += 1
            
            if is_correct:
                stats['correct_predictions'] += 1
                stats['signal_correct'][signal] += 1
    
    return agent_stats

def generate_final_report(agent_stats, experiment_name, ticker):
    """生成最终的准确性报告"""
    report = {
        "experiment_name": experiment_name,
        "ticker": ticker,
        "generation_date": datetime.now().isoformat(),
        "cumulative_stats": {},
        "total_evaluations": 0,
        "agents_tracked": len(agent_stats)
    }
    
    for agent_name, stats in agent_stats.items():
        total_predictions = stats['total_predictions']
        correct_predictions = stats['correct_predictions']
        
        if total_predictions == 0:
            continue
            
        accuracy_rate = correct_predictions / total_predictions
        
        # 计算每个信号类型的准确率
        signal_accuracies = {}
        signal_stats = {}
        
        for signal, count in stats['signal_counts'].items():
            if count > 0:
                correct_count = stats['signal_correct'][signal]
                signal_accuracy = correct_count / count
                signal_accuracies[signal] = signal_accuracy
                signal_stats[signal] = {
                    "total": count,
                    "correct": correct_count
                }
        
        agent_report = {
            "total_predictions": total_predictions,
            "correct_predictions": correct_predictions,
            "accuracy_rate": accuracy_rate,
            "signal_accuracies": signal_accuracies,
            "signal_stats": signal_stats
        }
        
        report["cumulative_stats"][agent_name] = agent_report
        report["total_evaluations"] += total_predictions
    
    return report

def main():
    # 设置路径
    folder_path = "reasoning_logs/accuracy_tracking_NVDA_20250101-20240601_llama4-scout"
    
    if not os.path.exists(folder_path):
        print(f"错误：文件夹 {folder_path} 不存在")
        return
    
    print(f"正在分析 {folder_path} 中的数据...")
    
    # 加载所有每日数据
    daily_data = load_daily_accuracy_files(folder_path)
    print(f"加载了 {len(daily_data)} 个每日准确性文件")
    
    if not daily_data:
        print("没有找到有效的每日准确性文件")
        return
    
    # 计算代理统计数据
    agent_stats = calculate_agent_statistics(daily_data)
    print(f"分析了 {len(agent_stats)} 个交易代理")
    
    # 生成最终报告
    experiment_name = "experiment_NVDA_20250101-20250601_llama4-scout"
    ticker = "NVDA"
    final_report = generate_final_report(agent_stats, experiment_name, ticker)
    
    # 保存报告
    output_filename = f"final_accuracy_report_{experiment_name}_{datetime.now().strftime('%Y-%m-%d')}.json"
    output_path = os.path.join(folder_path, output_filename)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False)
    
    print(f"最终准确性报告已保存到: {output_path}")
    
    # 打印摘要
    print("\n=== 准确性报告摘要 ===")
    print(f"实验名称: {final_report['experiment_name']}")
    print(f"股票代码: {final_report['ticker']}")
    print(f"总评估次数: {final_report['total_evaluations']}")
    print(f"跟踪的代理数量: {final_report['agents_tracked']}")
    
    print("\n=== 各代理准确率排名 ===")
    agent_accuracies = []
    for agent_name, stats in final_report['cumulative_stats'].items():
        agent_accuracies.append((agent_name, stats['accuracy_rate'], stats['total_predictions']))
    
    # 按准确率排序
    agent_accuracies.sort(key=lambda x: x[1], reverse=True)
    
    for i, (agent_name, accuracy, total) in enumerate(agent_accuracies, 1):
        print(f"{i:2d}. {agent_name:<30} {accuracy:.4f} ({total} 预测)")

if __name__ == "__main__":
    main()
