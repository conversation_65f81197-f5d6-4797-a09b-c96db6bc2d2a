#!/usr/bin/env python3
"""
比较 llama4-scout 和 grok-beta 两个模型的准确性表现
"""

import json
import os
from datetime import datetime

def load_accuracy_report(file_path):
    """加载准确性报告文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def compare_models(llama_report, grok_report):
    """比较两个模型的表现"""
    print("=" * 80)
    print("NVDA 股票预测准确性对比分析：llama4-scout vs grok-beta")
    print("=" * 80)
    
    # 基本信息对比
    print(f"\n📊 基本信息对比:")
    print(f"{'指标':<25} {'llama4-scout':<15} {'grok-beta':<15} {'差异':<15}")
    print("-" * 70)
    
    llama_total = llama_report['total_evaluations']
    grok_total = grok_report['total_evaluations']
    llama_agents = llama_report['agents_tracked']
    grok_agents = grok_report['agents_tracked']
    
    print(f"{'总评估次数':<25} {llama_total:<15} {grok_total:<15} {llama_total - grok_total:<15}")
    print(f"{'跟踪代理数量':<25} {llama_agents:<15} {grok_agents:<15} {llama_agents - grok_agents:<15}")
    
    # 计算整体平均准确率
    llama_avg_accuracy = sum(agent['accuracy_rate'] for agent in llama_report['cumulative_stats'].values()) / len(llama_report['cumulative_stats'])
    grok_avg_accuracy = sum(agent['accuracy_rate'] for agent in grok_report['cumulative_stats'].values()) / len(grok_report['cumulative_stats'])
    
    print(f"{'平均准确率':<25} {llama_avg_accuracy:.4f:<15} {grok_avg_accuracy:.4f:<15} {(llama_avg_accuracy - grok_avg_accuracy):+.4f:<15}")
    
    # 各代理准确率对比
    print(f"\n🤖 各代理准确率对比:")
    print(f"{'代理名称':<30} {'llama4-scout':<15} {'grok-beta':<15} {'差异':<15} {'表现':<10}")
    print("-" * 85)
    
    agent_comparisons = []
    
    for agent_name in llama_report['cumulative_stats'].keys():
        if agent_name in grok_report['cumulative_stats']:
            llama_acc = llama_report['cumulative_stats'][agent_name]['accuracy_rate']
            grok_acc = grok_report['cumulative_stats'][agent_name]['accuracy_rate']
            diff = llama_acc - grok_acc
            
            if diff > 0.01:
                performance = "🟢 llama胜"
            elif diff < -0.01:
                performance = "🔴 grok胜"
            else:
                performance = "🟡 相近"
            
            agent_comparisons.append((agent_name, llama_acc, grok_acc, diff, performance))
    
    # 按差异排序
    agent_comparisons.sort(key=lambda x: x[3], reverse=True)
    
    for agent_name, llama_acc, grok_acc, diff, performance in agent_comparisons:
        print(f"{agent_name:<30} {llama_acc:.4f:<15} {grok_acc:.4f:<15} {diff:+.4f:<15} {performance:<10}")
    
    # 统计胜负情况
    llama_wins = sum(1 for _, _, _, diff, _ in agent_comparisons if diff > 0.01)
    grok_wins = sum(1 for _, _, _, diff, _ in agent_comparisons if diff < -0.01)
    ties = len(agent_comparisons) - llama_wins - grok_wins
    
    print(f"\n📈 胜负统计:")
    print(f"llama4-scout 胜出: {llama_wins} 个代理")
    print(f"grok-beta 胜出: {grok_wins} 个代理")
    print(f"表现相近: {ties} 个代理")
    
    # 最佳和最差表现代理
    print(f"\n🏆 最佳表现代理:")
    llama_best = max(llama_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    grok_best = max(grok_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    
    print(f"llama4-scout: {llama_best[0]} ({llama_best[1]['accuracy_rate']:.4f})")
    print(f"grok-beta: {grok_best[0]} ({grok_best[1]['accuracy_rate']:.4f})")
    
    print(f"\n📉 最差表现代理:")
    llama_worst = min(llama_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    grok_worst = min(grok_report['cumulative_stats'].items(), key=lambda x: x[1]['accuracy_rate'])
    
    print(f"llama4-scout: {llama_worst[0]} ({llama_worst[1]['accuracy_rate']:.4f})")
    print(f"grok-beta: {grok_worst[0]} ({grok_worst[1]['accuracy_rate']:.4f})")
    
    # 信号类型分析
    print(f"\n📊 信号类型分布分析:")
    analyze_signal_distribution(llama_report, grok_report)

def analyze_signal_distribution(llama_report, grok_report):
    """分析信号类型分布"""
    llama_signals = {'bullish': 0, 'bearish': 0, 'neutral': 0}
    grok_signals = {'bullish': 0, 'bearish': 0, 'neutral': 0}
    
    # 统计 llama4-scout 信号分布
    for agent_stats in llama_report['cumulative_stats'].values():
        for signal, stats in agent_stats['signal_stats'].items():
            if signal in llama_signals:
                llama_signals[signal] += stats['total']
    
    # 统计 grok-beta 信号分布
    for agent_stats in grok_report['cumulative_stats'].values():
        for signal, stats in agent_stats['signal_stats'].items():
            if signal in grok_signals:
                grok_signals[signal] += stats['total']
    
    print(f"{'信号类型':<15} {'llama4-scout':<15} {'grok-beta':<15} {'差异':<15}")
    print("-" * 60)
    
    for signal in ['bullish', 'bearish', 'neutral']:
        llama_count = llama_signals[signal]
        grok_count = grok_signals[signal]
        diff = llama_count - grok_count
        print(f"{signal:<15} {llama_count:<15} {grok_count:<15} {diff:<15}")

def main():
    # 文件路径
    llama_report_path = "reasoning_logs/accuracy_tracking_NVDA_20250101-20240601_llama4-scout/final_accuracy_report_experiment_NVDA_20250101-20250601_llama4-scout_2025-06-30.json"
    grok_report_path = "reasoning_logs/accuracy_tracking_NVDA_20250101-20240601_grok-beta/final_accuracy_report_experiment_2025-06-28_NVDA.json"
    
    # 加载报告
    llama_report = load_accuracy_report(llama_report_path)
    grok_report = load_accuracy_report(grok_report_path)
    
    if not llama_report:
        print(f"无法加载 llama4-scout 报告: {llama_report_path}")
        return
    
    if not grok_report:
        print(f"无法加载 grok-beta 报告: {grok_report_path}")
        return
    
    # 进行对比分析
    compare_models(llama_report, grok_report)
    
    # 生成对比报告文件
    comparison_report = {
        "comparison_date": datetime.now().isoformat(),
        "models_compared": ["llama4-scout", "grok-beta"],
        "ticker": "NVDA",
        "period": "2025-01-01 to 2025-06-01",
        "llama4_scout": {
            "total_evaluations": llama_report['total_evaluations'],
            "agents_tracked": llama_report['agents_tracked'],
            "average_accuracy": sum(agent['accuracy_rate'] for agent in llama_report['cumulative_stats'].values()) / len(llama_report['cumulative_stats'])
        },
        "grok_beta": {
            "total_evaluations": grok_report['total_evaluations'],
            "agents_tracked": grok_report['agents_tracked'],
            "average_accuracy": sum(agent['accuracy_rate'] for agent in grok_report['cumulative_stats'].values()) / len(grok_report['cumulative_stats'])
        }
    }
    
    output_path = "model_accuracy_comparison_llama4_vs_grok_2025-06-30.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(comparison_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 对比报告已保存到: {output_path}")

if __name__ == "__main__":
    main()
